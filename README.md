# A股智能选股系统

基于Python的A股智能选股系统，采用分层架构设计，支持多数据源和多策略扩展。系统包含数据更新、策略选股、可视化验证三大核心功能模块。

## 🎯 核心功能模块

### 1. 📊 数据更新模块
- **历史数据更新**: 支持指定时间范围的批量历史数据补充
- **增量数据更新**: 智能检测并更新T-1交易日数据
- **批量处理能力**: 每批100只股票，充分发挥数据源批量更新能力
- **智能缓存**: 股票列表和交易日历缓存，减少重复请求
- **错误隔离**: 单只股票失败不影响整批处理

### 2. 🎯 策略选股模块
- **技术反转策略**: 基于RSI、布林带、成交量等技术指标识别反转机会
- **成交量异常策略**: 识别成交量异常放大的股票
- **多策略支持**: 可扩展的策略框架，支持自定义选股策略
- **候选股票池**: 支持控制选择部分股票或全部股票进行策略筛选
- **评分机制**: 综合多个指标进行评分排序

### 3. 📈 可视化验证模块
- **K线图可视化**: 专业的股票K线图表展示
- **策略信号标注**: 在图表中清晰标注符合策略条件的历史时间点
- **技术指标展示**: 布林带、RSI、成交量等技术指标可视化
- **批量验证**: 支持多只股票的批量策略验证
- **历史回测**: 使用历史数据验证策略有效性，用于策略开发调试

## 项目状态

当前版本：**v2.0** - 完整功能实现

**注意**：如果运行测试脚本时出现"没有接口访问权限"错误，请检查你的Tushare账号是否具有相应接口的权限。部分接口需要一定的积分才能访问，具体权限要求请参考[Tushare积分说明](https://tushare.pro/document/1?doc_id=108)。

## 📖 快速使用

**新用户推荐先阅读**：[快速使用指南 (QUICK_START.md)](QUICK_START.md)

该指南包含：
- 🚀 环境检查和配置
- 📊 三大功能模块使用方法
- 🔄 完整工作流程示例
- 📁 输出文件说明
- ⚠️ 注意事项和常见问题

## 📁 项目结构

```
select-in-ai/
├── README.md                   # 项目说明文档
├── requirements.txt            # Python依赖包
├── run_selection.py           # 策略选股主程序
├── src/                       # 核心源代码
│   ├── main.py               # 数据更新主程序
│   ├── core/                 # 核心模块
│   │   ├── interfaces/       # 抽象接口定义
│   │   ├── models/          # 数据模型
│   │   └── exceptions/      # 自定义异常
│   ├── data/                # 数据层模块
│   │   ├── sources/         # 数据源实现（Tushare等）
│   │   ├── access/          # 数据访问层（MySQL/SQLite）
│   │   └── services/        # 数据服务层
│   ├── strategies/          # 选股策略模块
│   │   ├── technical_reversal_strategy.py    # 技术反转策略
│   │   ├── volume_anomaly_strategy.py        # 成交量异常策略
│   │   └── strategy_manager.py               # 策略管理器
│   ├── validation/          # 可视化验证模块
│   │   ├── visual_validator.py               # 可视化验证器
│   │   ├── strategy_validator.py             # 策略验证器
│   │   └── validation_manager.py             # 验证管理器
│   └── utils/               # 工具模块
├── scripts/                 # 工具脚本
│   ├── setup_tushare_token.py               # Tushare配置
│   ├── interactive_visual_validation.py     # 交互式验证
│   └── demo_visual_validation.py            # 验证演示
├── config/                  # 配置文件
├── charts/                  # 生成的图表文件
├── results/                 # 验证结果文件
├── data/                    # 数据库文件
├── logs/                    # 日志文件
└── doc/                     # 详细文档
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 进入项目目录
cd select-in-ai

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 确认依赖已安装
pip list | grep -E "(tushare|mysql|pandas|matplotlib)"
```

### 2. Tushare配置

本系统使用Tushare作为数据源，需要配置API Token：

```bash
# 1. 访问 https://tushare.pro/ 注册账号并获取Token
# 2. 运行配置脚本
python scripts/setup_tushare_token.py

# 3. 验证配置
python scripts/test_tushare_basic.py
```

详细配置说明请参考：[Tushare数据源配置指南](doc/Tushare数据源配置指南.md)

### 3. 数据库配置

#### MySQL配置（推荐）
系统默认使用MySQL数据库，配置信息：
- 服务器地址：localhost
- 用户名：agent
- 密码：123456
- 数据库名：trade-ai

```bash
# 确保MySQL服务运行
# Docker方式启动MySQL（推荐）
docker run -d --name mysql-trade \
  -e MYSQL_ROOT_PASSWORD=123456 \
  -e MYSQL_DATABASE=trade-ai \
  -e MYSQL_USER=agent \
  -e MYSQL_PASSWORD=123456 \
  -p 3306:3306 mysql:8.0
```

### 4. 系统状态检查

运行系统检查工具，确保所有模块正常工作：

```bash
# 运行系统状态检查
python scripts/system_check.py
```

该工具会检查：
- ✅ 目录结构完整性
- ✅ 核心文件存在性
- ✅ 数据库连接状态
- ✅ Tushare配置状态
- ✅ 策略模块功能

## 💼 完整工作流程

### 工作流程1：数据更新 → 策略选股 → 可视化验证

这是系统的标准工作流程，适用于日常选股操作：

#### 步骤1：数据更新
```bash
# 首次使用：更新股票列表并补充历史数据
python src/main.py --update-stocks --historical --start-date 2024-01-01

# 日常使用：增量更新T-1交易日数据
python src/main.py --incremental
```

#### 步骤2：策略选股
```bash
# 查看可用策略
python run_selection.py info

# 执行技术反转策略选股
python run_selection.py select --strategy technical_reversal

# 执行成交量异常策略选股
python run_selection.py select --strategy volume_anomaly
```

#### 步骤3：可视化验证
```bash
# 对选中的股票进行可视化验证
python scripts/interactive_visual_validation.py

# 或运行演示验证
python scripts/demo_visual_validation.py
```

### 工作流程2：策略开发与验证

适用于开发新策略或验证现有策略：

#### 步骤1：策略验证
```bash
# 对特定股票进行策略验证
python scripts/run_validation.py single --stock 601111 --strategy technical_reversal --date 2024-04-07

# 批量验证多只股票
python scripts/run_validation.py batch --stocks 601111,600036,000001 --strategy technical_reversal
```

#### 步骤2：可视化分析
```bash
# 生成K线图表，查看策略信号点
python scripts/interactive_visual_validation.py
```

#### 步骤3：策略优化
根据验证结果调整策略参数，重复验证过程。

### 工作流程3：历史数据分析

适用于分析特定股票的历史表现：

```bash
# 补充特定股票的历史数据
python src/main.py --historical --stocks 601111 --start-date 2024-03-01 --end-date 2024-05-31

# 对该股票进行可视化验证
python scripts/interactive_visual_validation.py
# 选择"单股票验证" → 输入601111 → 选择时间范围
```

## 🔧 功能模块详解

### 1. 数据更新模块

#### 核心功能
- **股票列表管理**: 自动获取和更新A股股票基本信息
- **历史数据补充**: 支持指定时间范围的批量历史数据获取
- **增量数据更新**: 智能检测并更新T-1交易日数据
- **批量处理**: 每批100只股票，提高处理效率

#### 主要命令
```bash
# 数据更新主程序
python src/main.py [选项]

# 常用选项：
--update-stocks              # 更新股票列表
--incremental               # 增量更新T-1交易日数据
--historical                # 历史数据补充
--start-date YYYY-MM-DD     # 指定开始日期
--end-date YYYY-MM-DD       # 指定结束日期
--stocks CODE1,CODE2        # 指定股票代码
```

### 2. 策略选股模块

#### 可用策略
- **technical_reversal**: 技术反转策略
  - 基于RSI、布林带下轨突破、成交量放大
  - 适用于识别超跌反弹机会
- **volume_anomaly**: 成交量异常策略
  - 基于成交量异常放大
  - 适用于识别资金关注度突然提升的股票

#### 主要命令
```bash
# 策略选股主程序
python run_selection.py [命令] [选项]

# 常用命令：
info                        # 查看系统信息和可用策略
select --strategy [策略名]   # 执行选股策略
```

### 3. 可视化验证模块

#### 核心功能
- **K线图表**: 专业的股票K线图表展示
- **技术指标**: 布林带、RSI、成交量等指标可视化
- **信号标注**: 清晰标注策略信号点
- **批量验证**: 支持多只股票批量验证

#### 主要工具
```bash
# 交互式可视化验证
python scripts/interactive_visual_validation.py

# 演示验证功能
python scripts/demo_visual_validation.py

# 策略验证工具
python scripts/run_validation.py [模式] [选项]
```

## 📊 输出文件说明

### 图表文件 (charts/)
- **格式**: PNG图片文件
- **命名**: `{股票代码}_{策略名称}_{时间戳}.png`
- **内容**: K线图、技术指标、策略信号标注

### 验证结果 (results/)
- **格式**: JSON文件
- **命名**: `validation_results_{时间戳}.json`
- **内容**: 详细的验证数据和信号信息

### 日志文件 (logs/)
- **app.log**: 系统运行日志
- **debug_*.log**: 调试日志文件

## 🏗️ 架构设计

### 核心设计原则
- **接口抽象**: 数据源、存储、策略都通过接口隔离
- **模块化设计**: 三大功能模块独立，便于维护和扩展
- **可扩展性**: 支持新增数据源、存储方案和选股策略

### 主要接口
- `IDataSource`: 数据源抽象接口
- `IDataAccess`: 数据访问抽象接口
- `ISelectionStrategy`: 选股策略抽象接口
- `IStrategyValidator`: 策略验证抽象接口

## 📚 详细文档

### 核心文档
- [产品需求文档 (PRD)](doc/PRD_A股选股系统.md)
- [架构设计文档](doc/架构设计文档.md)
- [用户执行指南](doc/用户执行指南.md)

### 功能文档
- [数据获取功能说明](doc/数据获取功能说明.md)
- [策略选股模块说明](doc/策略选股模块说明.md)
- [可视化验证模块说明](doc/可视化验证模块说明.md)
- [策略可视化验证完整指南](doc/策略可视化验证完整指南.md)
- [策略验证功能使用指南](doc/策略验证功能使用指南.md)
- [选股系统使用总结](doc/选股系统使用总结.md)
- [项目整理总结](doc/项目整理总结.md)

### 配置文档
- [Tushare数据源配置指南](doc/Tushare数据源配置指南.md)
- [Token配置文件管理说明](doc/Token配置文件管理说明.md)

## 🛠️ 技术栈

- **Python 3.8+**
- **数据获取**: Tushare (主要数据源)
- **数据库**: MySQL (主要), SQLite (备用)
- **数据处理**: pandas, numpy
- **可视化**: matplotlib, mplfinance
- **数据库连接**: PyMySQL, SQLAlchemy
- **配置管理**: JSON配置文件

## ⚠️ 注意事项

1. **Tushare权限**: 部分接口需要一定积分才能访问
2. **数据频率**: 系统专注T-1交易日数据，不提供实时数据
3. **MySQL配置**: 推荐使用Docker部署MySQL服务
4. **虚拟环境**: 建议在Python虚拟环境中运行

## 📞 技术支持

如有问题或建议，请查阅相关文档或提交Issue。
