# 策略验证功能使用指南

## 功能简介

策略验证功能用于测试选股策略在历史数据中是否能正常工作，主要用于策略开发调试和定期验证策略有效性。

**核心目的**：验证策略逻辑是否正确，而非计算投资收益。

## 快速开始

### 1. 单日验证
测试策略在特定日期的选股表现：
```bash
python scripts/run_validation.py technical_reversal single --test-date 2024-04-07
```

### 2. 批量验证
测试策略在一段时间内的稳定性：
```bash
python scripts/run_validation.py technical_reversal batch \
    --start-date 2024-01-01 \
    --end-date 2024-06-30 \
    --frequency 7
```

### 3. 指定日期验证
测试策略在关键时间点的表现：
```bash
python scripts/run_validation.py technical_reversal dates \
    --test-dates "2024-04-07,2024-04-30,2024-05-15"
```

## 验证模式详解

### 1. 单日验证 (single)
- **用途**：测试策略在特定日期的选股表现
- **适用场景**：验证关键买入点、调试策略参数
- **输出**：选中股票列表及详细指标

### 2. 批量验证 (batch)
- **用途**：测试策略在一段时间内的选股稳定性
- **适用场景**：评估策略整体表现、发现时间规律
- **输出**：多个时间点的验证结果汇总

### 3. 指定日期验证 (dates)
- **用途**：测试策略在关键时间点的表现
- **适用场景**：验证特定市场事件下的策略表现
- **输出**：指定日期的详细验证结果

### 4. 策略比较 (compare)
- **用途**：比较多个策略的验证结果
- **适用场景**：策略选择、性能对比
- **输出**：策略比较表格和详细报告

### 5. 稳定性验证 (stability)
- **用途**：分析策略在不同时间窗口的稳定性
- **适用场景**：评估策略鲁棒性
- **输出**：稳定性分析报告

## 参数说明

### 通用参数
- `--max-stocks`: 每次测试最多选股数 (默认: 20)
- `--stock-pool`: 股票池，用逗号分隔
- `--no-save`: 不保存验证结果到数据库
- `--output`: 输出报告到文件
- `--verbose`: 详细输出

### 批量验证参数
- `--start-date`: 验证开始日期
- `--end-date`: 验证结束日期
- `--frequency`: 验证频率（天）

### 稳定性验证参数
- `--window-days`: 滑动窗口天数

## 验证报告解读

### 基本信息
- **策略名称**：被验证的策略
- **测试日期数量**：总测试次数
- **成功/失败测试**：运行状态统计
- **总执行时间**：验证耗时

### 统计信息
- **平均选股数量**：每次验证平均选中的股票数
- **平均选股比例**：选股数量占候选股票的比例

### 详细结果
每个测试日期的具体结果：
- 测试日期
- 选股数量
- 选股比例
- 执行时间
- 运行状态

### 选股详情
显示最近几次验证的详细选股结果：
- 股票代码和名称
- 策略评分
- RSI指标值
- 量比（成交量比率）
- 价格位置

### 验证评估
自动评估策略表现：
- **运行稳定性**：基于成功率评估
- **选股数量**：评估是否能选出足够的股票
- **选股比例**：评估策略条件是否合理

## 使用示例

### 验证技术反转策略
```bash
# 验证关键买入点
python scripts/run_validation.py technical_reversal dates \
    --test-dates "2024-04-07,2024-04-30" \
    --max-stocks 15 \
    --verbose

# 批量验证3个月表现
python scripts/run_validation.py technical_reversal batch \
    --start-date 2024-01-01 \
    --end-date 2024-03-31 \
    --frequency 7 \
    --output technical_reversal_validation.txt
```

### 比较多个策略
```bash
python scripts/run_validation.py technical_reversal compare \
    --strategies "technical_reversal,volume_anomaly" \
    --start-date 2024-01-01 \
    --end-date 2024-06-30 \
    --frequency 14 \
    --verbose
```

### 稳定性分析
```bash
python scripts/run_validation.py technical_reversal stability \
    --start-date 2024-01-01 \
    --end-date 2024-12-31 \
    --window-days 30 \
    --output stability_analysis.txt
```

## Python API 使用

```python
from datetime import datetime
from src.validation.validation_manager import ValidationManager
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config

# 初始化
config = db_config.get_mysql_config()
data_access = MySQLDataAccess(**config)
validation_manager = ValidationManager(data_access)

# 单日验证
summary = validation_manager.validate_single_date(
    strategy_name="technical_reversal",
    test_date=datetime(2024, 4, 7),
    max_stocks=15
)

# 生成报告
report = validation_manager.validator.generate_validation_report(summary)
print(report)
```

## 最佳实践

### 1. 策略开发阶段
- 使用单日验证测试关键时间点
- 使用批量验证评估整体表现
- 关注选股数量和比例的合理性

### 2. 策略调试
- 如果选不出股票，检查策略条件是否过严
- 如果选股过多，检查策略条件是否过松
- 观察RSI、量比等指标是否符合预期

### 3. 定期验证
- 定期运行批量验证检查策略有效性
- 使用稳定性验证评估策略鲁棒性
- 比较不同策略的表现

### 4. 参数优化
- 通过验证结果调整策略参数
- 关注验证评估的建议
- 保持选股数量在合理范围内

## 注意事项

1. **数据依赖**：确保有足够的历史数据
2. **执行时间**：大范围验证可能耗时较长
3. **结果解读**：验证结果不代表未来表现
4. **策略调整**：根据验证结果合理调整策略参数

## 故障排除

### 常见问题
1. **选不出股票**：策略条件可能过于严格
2. **执行缓慢**：可以限制股票池大小
3. **数据不足**：检查历史数据完整性
4. **连接错误**：检查数据库连接配置

### 解决方案
- 查看详细日志了解具体错误
- 使用 `--verbose` 参数获取更多信息
- 检查策略参数配置
- 确认数据库服务正常运行
