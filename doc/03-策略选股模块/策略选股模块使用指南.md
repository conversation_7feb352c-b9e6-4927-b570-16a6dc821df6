# 策略选股模块使用指南

## 🎯 模块概述

策略选股模块是A股智能选股系统的核心业务模块，负责根据预设的量化策略从股票池中筛选出符合条件的投资标的。该模块支持多种选股策略，提供灵活的参数配置和候选股票池控制功能。

## 🚀 快速开始

### 前置条件
```bash
# 1. 激活虚拟环境
source venv/bin/activate

# 2. 确认数据库中有足够的股票数据
python src/main.py --stats

# 3. 确认系统正常运行
python scripts/test_selection.py
```

### 基本使用
```bash
# 运行默认策略选股
python src/run_selection.py

# 运行指定策略
python src/run_selection.py --strategy technical_reversal

# 限制候选股票数量
python src/run_selection.py --max-candidates 50

# 显示详细信息
python src/run_selection.py --verbose
```

## 📋 策略详解

### 1. 技术反转策略 (technical_reversal)

**策略描述**: 基于布林带下轨突破、成交量放大和RSI超卖的技术反转信号

**核心条件**:
- **布林带下轨突破**: 收盘价触及或跌破布林带下轨
- **成交量放大**: 成交量为近期平均值的1.5-2倍
- **RSI超卖**: 14日RSI < 30（作为评分因子）

**评分机制**:
- 基础分: 50分
- 量比加分: (量比 - 1.5) × 20分
- RSI加分: (30 - RSI) × 2分
- 最终评分 = 基础分 + 量比加分 + RSI加分

**使用方法**:
```bash
# 运行技术反转策略
python src/run_selection.py --strategy technical_reversal

# 调整RSI阈值
python src/run_selection.py --strategy technical_reversal --rsi-threshold 25

# 调整量比范围
python src/run_selection.py --strategy technical_reversal --volume-min 1.2 --volume-max 2.5
```

### 2. 成交量异常策略 (volume_anomaly)

**策略描述**: 筛选成交量异常放大的股票

**核心条件**:
- 过去2周平均成交量低于3个月平均值的70%
- 今日成交量超过过去2周平均值的3倍
- 股价在5-100元范围内
- 非ST股票

**使用方法**:
```bash
# 运行成交量异常策略
python src/run_selection.py --strategy volume_anomaly

# 调整成交量倍数
python src/run_selection.py --strategy volume_anomaly --volume-multiplier 2.5
```

## 🔧 参数配置

### 命令行参数
```bash
python src/run_selection.py [选项]

选项:
  --strategy STRATEGY        选择策略 (technical_reversal, volume_anomaly)
  --max-candidates NUM       最大候选股票数量 (默认: 100)
  --rsi-threshold VALUE      RSI阈值 (默认: 30)
  --volume-min VALUE         最小量比 (默认: 1.5)
  --volume-max VALUE         最大量比 (默认: 2.0)
  --volume-multiplier VALUE  成交量倍数 (默认: 3.0)
  --price-min VALUE          最低股价 (默认: 5.0)
  --price-max VALUE          最高股价 (默认: 100.0)
  --verbose                  显示详细信息
  --help                     显示帮助信息
```

### 候选股票池控制

**功能说明**: 控制参与选股的股票范围，提高选股效率

**控制方式**:
1. **随机采样**: 从全部股票中随机选择指定数量
2. **活跃股票优先**: 优先选择成交量较大的活跃股票
3. **排除条件**: 自动排除ST股票、停牌股票等

**使用方法**:
```bash
# 限制候选股票为50只
python src/run_selection.py --max-candidates 50

# 使用活跃股票优先模式
python src/run_selection.py --max-candidates 100 --active-first

# 查看候选股票池信息
python src/run_selection.py --show-candidates
```

## 📊 结果解读

### 选股结果格式
```
🎯 策略选股结果 - technical_reversal
📅 选股日期: 2024-05-31
📊 候选股票: 100只 | 符合条件: 5只

排名 | 股票代码 | 股票名称 | 收盘价 | 评分  | 量比 | RSI  | 选股原因
-----|----------|----------|--------|-------|------|------|----------
1    | 601111   | 中国国航 | 7.09   | 103.0 | 1.8  | 18.5 | 布林带下轨+量比1.8+RSI超卖
2    | 600036   | 招商银行 | 45.20  | 89.2  | 1.6  | 22.3 | 布林带下轨+量比1.6+RSI超卖
3    | 000001   | 平安银行 | 12.85  | 76.8  | 1.5  | 25.1 | 布林带下轨+量比1.5+RSI超卖
```

### 评分说明
- **评分范围**: 50-150分
- **高分股票**: 评分>90分，技术指标强烈
- **中等股票**: 评分70-90分，技术指标较好
- **低分股票**: 评分50-70分，刚好符合条件

### 关键指标
- **量比**: 当日成交量/近期平均成交量
- **RSI**: 相对强弱指数，衡量超买超卖
- **布林带位置**: 价格相对于布林带的位置

## 💡 使用建议

### 策略选择
1. **技术反转策略**: 适合短期反弹机会，风险相对较高
2. **成交量异常策略**: 适合捕捉突发利好，需要快速反应

### 参数调整
1. **保守型**: 提高RSI阈值(25)，降低量比要求(1.2-1.8)
2. **激进型**: 降低RSI阈值(35)，提高量比要求(1.8-2.5)
3. **平衡型**: 使用默认参数

### 候选股票池
1. **全面扫描**: 使用较大的候选股票数量(200-500)
2. **精准筛选**: 使用较小的候选股票数量(50-100)
3. **活跃股票**: 优先选择成交活跃的股票

## 🔍 策略验证

### 历史回测
```bash
# 验证策略在历史数据中的表现
python scripts/strategy_backtest.py --strategy technical_reversal --start-date 2024-01-01 --end-date 2024-03-31

# 批量验证多个时间段
python scripts/batch_validation.py --strategy technical_reversal
```

### 单股票验证
```bash
# 验证特定股票的策略信号
python scripts/single_stock_validation.py --stock 601111 --strategy technical_reversal --start-date 2024-04-01 --end-date 2024-04-30
```

## ❓ 常见问题

### Q1: 选不出股票怎么办？
**可能原因**:
- 市场整体强势，缺乏反转机会
- 策略参数过于严格
- 数据不完整

**解决方案**:
```bash
# 放宽策略参数
python src/run_selection.py --rsi-threshold 35 --volume-min 1.2

# 增加候选股票数量
python src/run_selection.py --max-candidates 500

# 检查数据完整性
python src/main.py --stats
```

### Q2: 选出的股票质量不高怎么办？
**解决方案**:
- 提高评分阈值，只关注高分股票
- 结合多个策略进行交叉验证
- 增加人工筛选环节

### Q3: 如何自定义策略？
**步骤**:
1. 在`src/strategies/`目录下创建新策略文件
2. 继承`ISelectionStrategy`接口
3. 实现`select_stocks`方法
4. 在策略管理器中注册新策略

## 🔧 高级用法

### 批量运行
```bash
# 运行所有策略
for strategy in technical_reversal volume_anomaly; do
    python src/run_selection.py --strategy $strategy
done
```

### 结果比较
```bash
# 比较不同参数下的选股结果
python scripts/compare_strategies.py --strategies technical_reversal,volume_anomaly
```

### 自动化运行
```bash
# 添加到定时任务
echo "0 16 * * 1-5 cd /path/to/project && python src/run_selection.py" | crontab -
```

## 📞 技术支持

如遇问题，请：
1. 查看日志文件 `logs/selection.log`
2. 运行策略测试脚本
3. 检查数据完整性
4. 验证策略参数设置

---

**推荐使用流程**:
1. 新手用户: 使用默认参数 → 观察结果 → 逐步调整
2. 熟练用户: 根据市场情况调整参数 → 多策略对比
3. 高级用户: 自定义策略 → 历史回测 → 实盘验证
