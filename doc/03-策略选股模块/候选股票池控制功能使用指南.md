# 候选股票池控制功能使用指南

## 功能概述

候选股票池控制功能允许您在策略验证时灵活控制参与验证的股票范围，支持两种模式：
1. **指定股票池**: 明确指定要验证的股票列表
2. **候选池大小控制**: 从全市场随机选择指定数量的股票进行验证

## 使用场景

### 1. 快速验证和调试
- **场景**: 策略开发阶段，需要快速验证策略逻辑
- **方案**: 使用较小的候选股票池（如50-100只）
- **优势**: 大幅提升验证速度，便于快速迭代

### 2. 特定股票验证
- **场景**: 验证策略在特定股票上的表现
- **方案**: 使用指定股票池功能
- **优势**: 精确控制验证范围，结果更有针对性

### 3. 全面验证
- **场景**: 策略最终验证，需要全面评估策略表现
- **方案**: 使用较大的候选股票池或全市场验证
- **优势**: 结果更具代表性和可靠性

## 使用方法

### 1. 命令行工具使用

#### 控制候选股票池大小
```bash
# 使用100只随机股票进行验证
python scripts/run_validation.py technical_reversal single \
    --test-date 2024-04-07 \
    --candidate-pool-size 100

# 使用50只随机股票进行批量验证
python scripts/run_validation.py technical_reversal batch \
    --start-date 2024-04-01 \
    --end-date 2024-04-30 \
    --candidate-pool-size 50
```

#### 指定特定股票池
```bash
# 验证指定的几只股票
python scripts/run_validation.py technical_reversal single \
    --test-date 2024-04-07 \
    --stock-pool 000001,000002,600000,600036,000858
```

### 2. Python API 使用

#### 控制候选股票池大小
```python
from datetime import datetime
from src.validation.validation_manager import ValidationManager
from src.data.access.mysql_access import MySQLDataAccess
from src.config.database_config import db_config

# 初始化
config = db_config.get_mysql_config()
data_access = MySQLDataAccess(**config)
validation_manager = ValidationManager(data_access)

# 使用100只随机股票验证
summary = validation_manager.validate_single_date(
    strategy_name="technical_reversal",
    test_date=datetime(2024, 4, 7),
    max_stocks=15,
    candidate_pool_size=100  # 关键参数
)
```

#### 指定特定股票池
```python
# 指定股票池验证
specific_stocks = ['000001', '000002', '600000', '600036', '000858']

summary = validation_manager.validate_single_date(
    strategy_name="technical_reversal",
    test_date=datetime(2024, 4, 7),
    max_stocks=10,
    stock_pool=specific_stocks  # 关键参数
)
```

## 参数说明

### candidate_pool_size
- **类型**: `Optional[int]`
- **默认值**: `None` (使用500只股票)
- **说明**: 从全市场随机选择的候选股票数量
- **取值建议**:
  - 快速验证: 50-100
  - 常规验证: 200-500
  - 全面验证: 1000+ 或 None

### stock_pool
- **类型**: `Optional[List[str]]`
- **默认值**: `None` (使用随机选择)
- **说明**: 明确指定的股票代码列表
- **优先级**: 高于 `candidate_pool_size`

## 性能对比

根据测试结果，不同候选股票池大小的性能表现：

| 候选池大小 | 候选股票数 | 执行时间 | 性能提升 |
|-----------|-----------|----------|----------|
| 50        | 50        | 0.35秒   | 9.6倍    |
| 100       | 100       | 0.64秒   | 5.3倍    |
| 200       | 200       | 1.24秒   | 2.7倍    |
| 500       | 500       | 3.36秒   | 1.0倍    |
| 全部      | 500       | 3.07秒   | 1.1倍    |

**结论**: 使用50只股票的候选池可以获得近10倍的性能提升。

## 结果一致性

### 随机种子机制
- 使用测试日期作为随机种子
- 确保同一天、同一配置的验证结果完全一致
- 不同候选池大小可能选出不同股票，但逻辑一致

### 选股重叠分析
测试显示，较小候选池选出的股票通常也会在较大候选池中被选中，说明策略逻辑稳定。

## 最佳实践

### 1. 开发阶段
```python
# 快速验证策略逻辑
summary = validation_manager.validate_single_date(
    strategy_name="technical_reversal",
    test_date=datetime(2024, 4, 7),
    candidate_pool_size=50,  # 快速验证
    save_results=False
)
```

### 2. 测试阶段
```python
# 中等规模验证
summary = validation_manager.validate_strategy_batch(
    strategy_name="technical_reversal",
    start_date=datetime(2024, 4, 1),
    end_date=datetime(2024, 4, 30),
    candidate_pool_size=200,  # 平衡速度和准确性
    save_results=False
)
```

### 3. 生产阶段
```python
# 全面验证
summary = validation_manager.validate_strategy_batch(
    strategy_name="technical_reversal",
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 6, 30),
    candidate_pool_size=None,  # 使用全部股票
    save_results=True
)
```

## 注意事项

1. **结果代表性**: 候选池越小，结果的代表性可能越低
2. **策略适用性**: 某些策略可能对股票池大小敏感
3. **随机性影响**: 小候选池的随机性影响更大
4. **数据完整性**: 确保候选股票有足够的历史数据

## 故障排除

### 常见问题

1. **候选股票数量不足**
   - 检查数据库中的股票数量
   - 确认股票有足够的历史数据

2. **验证结果为空**
   - 尝试增加候选股票池大小
   - 检查策略条件是否过于严格

3. **性能问题**
   - 减少候选股票池大小
   - 检查数据库连接和查询性能

## 总结

候选股票池控制功能为策略验证提供了灵活性和效率：
- **开发时**: 使用小候选池快速验证
- **测试时**: 使用中等候选池平衡效率和准确性  
- **生产时**: 使用大候选池或全市场验证
- **特定需求**: 使用指定股票池精确控制

通过合理使用这些功能，可以显著提升策略开发和验证的效率。
