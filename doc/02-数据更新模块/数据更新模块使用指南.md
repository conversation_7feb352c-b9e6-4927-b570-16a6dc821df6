# 数据更新模块使用指南

## 🎯 模块概述

数据更新模块是A股智能选股系统的核心基础模块，负责从外部数据源获取股票交易数据并存储到本地数据库。该模块专注于T-1交易日数据获取，支持增量更新和历史数据补充两种模式。

## 🚀 快速开始

### 前置条件
```bash
# 1. 激活虚拟环境
source venv/bin/activate

# 2. 确认数据库连接正常
python scripts/test_database.py

# 3. 确认Tushare配置正确
python scripts/test_tushare.py
```

### 基本使用
```bash
# 更新股票列表
python src/main.py --update-stocks

# 增量更新T-1交易日数据
python src/main.py --incremental

# 补充历史数据（默认最近90天）
python src/main.py --historical

# 查看数据统计
python src/main.py --stats
```

## 📋 功能详解

### 1. 增量更新功能

**适用场景**: 每日定时任务，获取最新交易日数据

**核心特性**:
- 智能检测需要更新的股票
- 只获取T-1交易日数据
- 批量处理，每批100只股票
- 错误隔离，单只股票失败不影响整批

**使用方法**:
```bash
# 基本增量更新
python src/main.py --incremental

# 增量更新并显示统计信息
python src/main.py --incremental --stats
```

**工作流程**:
1. 获取最新交易日期
2. 批量检查所有股票的最新数据日期
3. 识别需要更新的股票
4. 分批处理，批量获取数据
5. 保存到数据库

### 2. 历史数据补充功能

**适用场景**: 系统初始化、数据修复、特定时间段数据补充

**核心特性**:
- 支持自定义时间范围
- 支持指定特定股票
- 智能缺失检测
- 批量处理优化

**使用方法**:
```bash
# 补充所有股票最近90天数据（默认）
python src/main.py --historical

# 补充指定时间范围的历史数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31

# 补充指定股票的历史数据
python src/main.py --historical --stocks 000001,000002,600000 --start-date 2024-01-01

# 补充指定股票指定时间范围的历史数据
python src/main.py --historical --stocks 000001,000002 --start-date 2024-01-01 --end-date 2024-03-31
```

**参数说明**:
- `--start-date`: 开始日期，格式YYYY-MM-DD，默认为90天前
- `--end-date`: 结束日期，格式YYYY-MM-DD，默认为当前日期
- `--stocks`: 股票代码列表，用逗号分隔，默认为所有股票

### 3. 股票列表更新功能

**适用场景**: 系统初始化、定期更新股票基本信息

**使用方法**:
```bash
# 更新股票列表
python src/main.py --update-stocks

# 更新股票列表并显示统计
python src/main.py --update-stocks --stats
```

### 4. 数据统计功能

**适用场景**: 数据质量检查、系统状态监控

**使用方法**:
```bash
# 显示数据统计信息
python src/main.py --stats
```

**统计内容**:
- 股票总数
- 数据日期范围
- 各股票数据完整性
- 最新更新时间

## 🔧 配置管理

### Tushare配置
配置文件位置: `config/tushare_config.json`

```json
{
    "token": "your_tushare_token_here",
    "timeout": 30,
    "retry_count": 3,
    "retry_delay": 1.0
}
```

### 数据库配置
配置文件位置: `config/database_config.json`

```json
{
    "mysql": {
        "host": "localhost",
        "port": 3306,
        "user": "agent",
        "password": "123456",
        "database": "trade-ai"
    }
}
```

## 📊 性能优化

### 批量处理
- 每批处理100只股票
- 批次间1-2秒延时
- 随机延时0.3-0.8秒

### 缓存机制
- 股票列表缓存
- 交易日历缓存
- 智能缓存清理

### 请求频率控制
- 每分钟最多500次请求
- 自动重试机制
- 错误隔离处理

## 💡 使用建议

### 日常运维
1. **每日增量更新**: 建议在交易日结束后运行
2. **定期数据检查**: 每周运行一次历史数据补充
3. **监控日志**: 关注日志输出，及时发现异常

### 初始化部署
1. **股票列表**: 首先更新股票列表
2. **历史数据**: 根据需要获取历史数据（建议从最近3个月开始）
3. **数据验证**: 运行测试脚本验证数据完整性

### 故障恢复
1. **网络异常**: 重新运行相应的更新命令
2. **数据缺失**: 使用历史数据补充功能修复
3. **数据库异常**: 检查数据库连接和权限

## ❓ 常见问题

### Q1: 数据获取失败怎么办？
```bash
# 检查网络连接
ping tushare.pro

# 检查Token配置
python scripts/test_tushare.py

# 检查数据库连接
python scripts/test_database.py
```

### Q2: 数据不完整怎么办？
```bash
# 检查数据统计
python src/main.py --stats

# 补充缺失数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31
```

### Q3: 如何提高数据获取速度？
- 确保网络连接稳定
- 使用增量更新而非全量更新
- 避免在交易时间获取数据

## 🔍 测试验证

### 功能测试
```bash
# 运行数据更新测试
python scripts/test_data_update.py

# 测试交易日历功能
python scripts/test_trading_calendar.py
```

### 数据验证
```bash
# 验证特定股票数据
python src/main.py --historical --stocks 000001 --start-date 2024-01-01 --end-date 2024-01-31

# 检查数据完整性
python scripts/check_data_integrity.py
```

## 📞 技术支持

如遇问题，请：
1. 查看日志文件 `logs/app.log`
2. 运行相关测试脚本
3. 检查配置文件设置
4. 确认网络和数据库连接

---

**推荐使用流程**:
1. 新系统: 更新股票列表 → 历史数据补充 → 增量更新
2. 日常维护: 增量更新 → 定期数据检查
3. 故障恢复: 诊断问题 → 数据修复 → 验证完整性
