# A股选股系统快速使用指南

## 🚀 快速开始

### 1. 环境检查
```bash
# 激活虚拟环境
source venv/bin/activate

# 运行系统检查
python scripts/system_check.py
```

### 2. 配置Tushare（如果未配置）
```bash
python scripts/setup_tushare_token.py
```

## 📊 三大核心功能

### 1. 数据更新模块

#### 日常数据更新（推荐）
```bash
# 增量更新T-1交易日数据
python src/main.py --incremental
```

#### 历史数据补充
```bash
# 补充指定时间范围的历史数据
python src/main.py --historical --start-date 2024-01-01 --end-date 2024-03-31

# 补充指定股票的历史数据
python src/main.py --historical --stocks 601111,600036 --start-date 2024-01-01
```

#### 首次使用
```bash
# 更新股票列表并补充历史数据
python src/main.py --update-stocks --historical --start-date 2024-01-01
```

### 2. 策略选股模块

#### 查看可用策略
```bash
python run_selection.py info
```

#### 执行选股策略
```bash
# 技术反转策略（基于RSI、布林带、成交量）
python run_selection.py select --strategy technical_reversal

# 成交量异常策略（基于成交量放大）
python run_selection.py select --strategy volume_anomaly
```

### 3. 可视化验证模块

#### 交互式验证（推荐）
```bash
python scripts/interactive_visual_validation.py
```
功能菜单：
- 单股票验证
- 批量股票验证  
- 策略效果比较
- 查看历史验证结果

#### 演示验证
```bash
python scripts/demo_visual_validation.py
```

#### 命令行验证
```bash
# 单日验证
python scripts/run_validation.py single --stock 601111 --strategy technical_reversal --date 2024-04-07

# 批量验证
python scripts/run_validation.py batch --stocks 601111,600036 --strategy technical_reversal
```

## 🔄 完整工作流程示例

### 场景1：日常选股操作
```bash
# 1. 更新数据
python src/main.py --incremental

# 2. 执行选股
python run_selection.py select --strategy technical_reversal

# 3. 可视化验证选中的股票
python scripts/interactive_visual_validation.py
```

### 场景2：策略开发与验证
```bash
# 1. 补充测试数据
python src/main.py --historical --stocks 601111 --start-date 2024-03-01

# 2. 验证策略效果
python scripts/run_validation.py batch --stocks 601111 --strategy technical_reversal

# 3. 可视化分析
python scripts/interactive_visual_validation.py
```

### 场景3：批量股票分析
```bash
# 1. 准备数据
python src/main.py --historical --stocks 601111,600036,000001 --start-date 2024-01-01

# 2. 批量验证
python scripts/interactive_visual_validation.py
# 选择"批量股票验证"

# 3. 查看生成的图表和报告
ls charts/
ls results/
```

## 📁 输出文件说明

### 图表文件 (charts/)
- 格式：PNG图片
- 命名：`{股票代码}_{策略名称}_{时间戳}.png`
- 内容：K线图、技术指标、策略信号标注

### 验证结果 (results/)
- 格式：JSON文件
- 命名：`validation_results_{时间戳}.json`
- 内容：详细验证数据和信号信息

### 日志文件 (logs/)
- app.log：系统运行日志

## ⚠️ 注意事项

1. **数据频率**：系统专注T-1交易日数据，不提供实时数据
2. **Tushare权限**：部分接口需要一定积分才能访问
3. **MySQL配置**：确保MySQL服务正常运行
4. **虚拟环境**：建议在Python虚拟环境中运行

## 🆘 常见问题

### 数据库连接失败
```bash
# 检查MySQL服务状态
docker ps | grep mysql

# 重启MySQL服务
docker restart mysql-trade
```

### Tushare权限不足
- 访问 https://tushare.pro/ 查看积分要求
- 升级账号权限或使用其他接口

### 模块导入错误
```bash
# 确保在项目根目录运行
cd /path/to/select-in-ai
source venv/bin/activate
```

## 📞 获取帮助

- 查看详细文档：`doc/` 目录
- 运行系统检查：`python scripts/system_check.py`
- 查看命令帮助：`python [脚本名] --help`
